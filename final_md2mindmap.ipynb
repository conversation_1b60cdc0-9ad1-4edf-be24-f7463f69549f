text_to_raw_md_prompt_header = """你现在是一位资深的**结构化知识工程专家**，擅长将任意文本拆解为**二层次思维导图**并以md格式输出，保证层级清晰、逻辑合理。你将认真阅读文本，从上往下提炼要点（用md二级标题表示）和要点的支撑论点（用md三级标题表示。）


### 你的任务：
- 根据输入的{文本内容}，将其提炼并组织为**两层结构化md**
- **严格遵循模板格式**，只生成md的二级和三级标题
- 所有总结和细节必须基于原始文本，保持语义完整但简洁


### 输出规范：
- 第一层（核心要点）：从文本中提出来的一个核心要点，一般10句话或是两段话都是围绕一个核心要点展开。
- 第二层（支撑要点）：每个核心要点有3到10个子要点，子要点是对核心要点的支撑说明。


<md模板>
## 一句话简洁明了描述文本中的一个重要的要点
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。


## 一句话简洁明了描述文本中的一个重要的要点
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。


## 一句话简洁明了描述文本中的一个重要的要点
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
### 一句话简洁明了描述对核心要点的一个支撑论点。
<md模板/>


<文本内容>
"""

text_to_raw_md_prompt_footer =""" 
<文本内容/>


**只输出最终md，不要输出任何额外说明或解释。**
"""


# 获取文本
file_path = r"C:\Users\<USER>\Desktop\ai_mindmap\documents\python第八章.md"

with open(file_path, 'r', encoding='utf-8') as file:
        text = file.read()
        
prompt = text_to_raw_md_prompt_header + text + text_to_raw_md_prompt_footer

print(prompt)

from openai import OpenAI
API_KEY = "sk-5LLuQ7Zk9SdvMbKAigi2fZFO27jdCZRmrxxPX85jX11sFywg"
BASE_URL = "https://api.openai-proxy.org"
MODEL = "gpt-4.1"

client = OpenAI(
    base_url='https://api.openai-proxy.org/v1',
    api_key=API_KEY,
)

chat_completion = client.chat.completions.create(
    messages=[
        {
            "role": "user",
            "content": prompt,
        }
    ],
    model=MODEL,
)

print(chat_completion.choices[0].message.content)

# 把一级标题进行精简的prompt

raw_md = chat_completion.choices[0].message.content
prompt = """根据原文输出一个合适的一级标题。将下文所有二级标题用一个合适的短语来替代，三级标题保持不变。输出修改后的未渲染的md文件的内容，不要输出对于整个md文件内容解释或说明。

"""

prompt = prompt + raw_md

print(prompt)

from openai import OpenAI
API_KEY = "sk-5LLuQ7Zk9SdvMbKAigi2fZFO27jdCZRmrxxPX85jX11sFywg"
BASE_URL = "https://api.openai-proxy.org"
MODEL = "gpt-4.1-mini" #这一个模型不需要很智能，有很多替代品

client = OpenAI(
    base_url='https://api.openai-proxy.org/v1',
    api_key=API_KEY,
)

chat_completion = client.chat.completions.create(
    messages=[
        {
            "role": "user",
            "content": prompt,
        }
    ],
    model=MODEL,
)

print(chat_completion.choices[0].message.content)
refined_md = chat_completion.choices[0].message.content